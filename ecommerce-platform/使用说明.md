# 🎉 电商平台管理系统 - 使用说明

## 🚀 项目已完成！

恭喜！你的Vue3+Vite电商平台已经成功创建，包含完整的用户认证和商品管理功能。

## 📱 访问地址

**开发环境**: http://localhost:5173

## 🔐 登录信息

### 演示账号（模拟数据）
- **管理员账号**: 
  - 用户名: `admin`
  - 密码: `123456`

- **普通用户账号**: 
  - 用户名: `user1`
  - 密码: `123456`

## ✨ 功能特性

### 🔑 用户认证系统
- ✅ 用户注册（支持邮箱验证）
- ✅ 用户登录（记住登录状态）
- ✅ 用户登出
- ✅ 个人资料管理
- ✅ 密码修改
- ✅ 路由守卫（自动跳转）

### 📦 商品管理系统
- ✅ 商品列表展示（分页、搜索、筛选）
- ✅ 添加新商品
- ✅ 编辑商品信息
- ✅ 删除商品
- ✅ 商品分类管理
- ✅ 商品状态管理（上架/下架）

### 🎨 用户体验
- ✅ 响应式设计（支持移动端）
- ✅ 加载状态提示
- ✅ 错误处理和提示
- ✅ 表单验证
- ✅ 图片预览
- ✅ 平滑过渡动画

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **后端服务**: LeanCloud (Serverless)

## 📋 使用流程

### 1. 首次访问
1. 打开 http://localhost:5173
2. 系统会自动跳转到登录页面
3. 可以选择登录或注册

### 2. 登录系统
1. 使用演示账号登录，或点击演示账号标签快速填充
2. 登录成功后进入仪表板

### 3. 商品管理
1. 点击左侧菜单"商品管理"
2. 查看商品列表，支持搜索和筛选
3. 点击"添加商品"创建新商品
4. 点击商品卡片上的"编辑"或"删除"按钮进行操作

### 4. 个人资料
1. 点击右上角用户头像
2. 选择"个人资料"
3. 可以编辑基本信息和修改密码

## 🔧 配置说明

### 数据源切换
在 `src/config/api.js` 中可以切换数据源：

```javascript
export const DEV_CONFIG = {
  USE_MOCK_API: true,  // true=使用模拟数据, false=使用LeanCloud
  DEBUG: true
}
```

### LeanCloud配置
如果要使用真实的LeanCloud数据库：

1. 确保已在LeanCloud创建`dianshang`数据表
2. 添加必要的字段（name, description, price, category, stock, status, image）
3. 将 `USE_MOCK_API` 设置为 `false`

## 🎯 项目结构

```
src/
├── api/              # API服务层
│   ├── authApi.js    # 用户认证API
│   ├── productApi.js # 商品API
│   └── mockApi.js    # 模拟数据API
├── components/       # 组件
│   ├── LoginForm.vue    # 登录表单
│   ├── RegisterForm.vue # 注册表单
│   ├── ProductList.vue  # 商品列表
│   └── ProductForm.vue  # 商品表单
├── views/           # 页面
│   ├── AuthPage.vue        # 认证页面
│   ├── Dashboard.vue       # 仪表板布局
│   ├── ProductManagement.vue # 商品管理
│   ├── Profile.vue         # 个人资料
│   └── NotFound.vue        # 404页面
├── models/          # 数据模型
│   ├── User.js      # 用户模型
│   └── Product.js   # 商品模型
├── router/          # 路由配置
│   └── index.js     # 路由定义
├── config/          # 配置文件
│   └── api.js       # API配置
├── utils/           # 工具函数
│   └── request.js   # HTTP请求封装
├── App.vue          # 根组件
└── main.js          # 入口文件
```

## 🚀 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 🔍 功能演示

### 登录注册
- 支持用户名/密码登录
- 注册时验证邮箱格式和密码强度
- 登录状态持久化

### 商品管理
- 支持按分类、状态筛选
- 支持商品名称和描述搜索
- 分页显示，每页可调整数量
- 表单验证确保数据完整性

### 个人中心
- 编辑个人基本信息
- 修改登录密码
- 头像显示和更新

## 🎨 界面特色

- **现代化设计**: 采用Element Plus组件库
- **渐变背景**: 美观的渐变色背景
- **卡片布局**: 清晰的信息层次
- **响应式**: 适配不同屏幕尺寸
- **动画效果**: 平滑的页面切换动画

## 🆘 常见问题

### Q: 登录后页面空白？
A: 检查浏览器控制台是否有错误，确保所有依赖正确安装。

### Q: 商品图片不显示？
A: 当前使用本地SVG占位图，可以在商品表单中输入有效的图片URL。

### Q: 如何添加新的商品分类？
A: 编辑 `src/models/Product.js` 中的 `PRODUCT_CATEGORIES` 数组。

### Q: 如何切换到真实API？
A: 配置好LeanCloud后，修改 `src/config/api.js` 中的 `USE_MOCK_API` 为 `false`。

## 🎊 恭喜完成！

你现在拥有一个功能完整的电商平台管理系统！可以继续扩展更多功能，如：

- 订单管理
- 购物车功能
- 支付集成
- 数据统计
- 权限管理
- 文件上传

祝你使用愉快！🎉
