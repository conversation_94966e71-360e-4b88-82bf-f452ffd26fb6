# 快速设置指南

## 🎉 恭喜！项目已经配置完成

你的LeanCloud应用凭证已经配置好了：
- **App ID**: uAXscy7KXjSWw6Loswnc5Q4x-gzGzoHsz
- **App Key**: b4rWvzNXLa0AbLhPZk2KRrx
- **服务器地址**: https://uaxscy7k.lc-cn-n1-shared.com

## 📋 接下来需要做的事情

### 1. 在LeanCloud中为dianshang类添加字段

访问你的LeanCloud控制台：
https://console.leancloud.cn/apps/uAXscy7KXjSWw6Loswnc5Q4x-gzGzoHsz/storage/data

点击 `dianshang` 类，然后添加以下字段：

#### 必需字段：
- **name** (String) - 商品名称
- **description** (String) - 商品描述  
- **price** (Number) - 商品价格
- **category** (String) - 商品分类
- **stock** (Number) - 库存数量
- **status** (String) - 商品状态 (active/inactive)
- **image** (String) - 商品图片URL

### 2. 添加测试数据

在LeanCloud控制台中，点击"添加行"来创建一些测试商品：

```json
{
  "name": "iPhone 15 Pro",
  "description": "苹果最新款智能手机",
  "price": 7999,
  "category": "electronics",
  "stock": 50,
  "status": "active",
  "image": "https://via.placeholder.com/300x200/007bff/ffffff?text=iPhone"
}
```

### 3. 切换到真实API

当你完成LeanCloud设置后，编辑 `src/config/api.js`：

```javascript
export const DEV_CONFIG = {
  USE_MOCK_API: false, // 改为false使用真实API
  DEBUG: true
}
```

## 🚀 功能特性

✅ **商品管理**
- 查看商品列表（分页、搜索、筛选）
- 添加新商品
- 编辑商品信息
- 删除商品

✅ **用户体验**
- 响应式设计
- 加载状态
- 错误处理
- 表单验证

✅ **技术栈**
- Vue 3 + Composition API
- Vite 构建工具
- Element Plus UI组件
- Axios HTTP客户端
- LeanCloud 云数据库

## 🔧 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📱 访问地址

- **开发环境**: http://localhost:5173
- **Vue DevTools**: http://localhost:5173/__devtools__

## 🎯 下一步扩展

- 用户认证系统
- 订单管理
- 购物车功能
- 支付集成
- 图片上传
- 数据统计

## 🆘 需要帮助？

如果遇到问题：
1. 检查浏览器控制台的错误信息
2. 确认LeanCloud数据表字段设置正确
3. 验证网络连接和API调用

项目现在已经可以正常使用了！🎉
