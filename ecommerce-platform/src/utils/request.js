import axios from 'axios'
import { API_CONFIG } from '@/config/api.js'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: API_CONFIG.SERVER_URL + '/1.1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-LC-Id': API_CONFIG.APP_ID,
    'X-LC-Key': API_CONFIG.APP_KEY
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    console.log('发送请求:', config)
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    console.log('响应数据:', response)
    return response.data
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error)
    
    let message = '请求失败'
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请检查应用凭证'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败: ${error.response.status}`
      }
    } else if (error.request) {
      message = '网络连接失败'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default request
