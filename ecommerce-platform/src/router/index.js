import { createRouter, createWebHistory } from 'vue-router'
import { AuthApi } from '@/api/authApi.js'
import { ElMessage } from 'element-plus'

// 导入组件
import AuthPage from '@/views/AuthPage.vue'
import Dashboard from '@/views/Dashboard.vue'
import ProductManagement from '@/views/ProductManagement.vue'
import Profile from '@/views/Profile.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/auth',
    name: 'Auth',
    component: AuthPage,
    meta: {
      requiresGuest: true, // 只有未登录用户可以访问
      title: '登录注册'
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      requiresAuth: true,
      title: '仪表板'
    },
    children: [
      {
        path: '',
        redirect: '/dashboard/products'
      },
      {
        path: 'products',
        name: 'ProductManagement',
        component: ProductManagement,
        meta: {
          requiresAuth: true,
          title: '商品管理'
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: Profile,
        meta: {
          requiresAuth: true,
          title: '个人资料'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 电商平台管理系统`
  }

  const isLoggedIn = AuthApi.isLoggedIn()
  
  // 需要登录的页面
  if (to.meta.requiresAuth && !isLoggedIn) {
    ElMessage.warning('请先登录')
    next('/auth')
    return
  }
  
  // 只有未登录用户可以访问的页面（如登录页）
  if (to.meta.requiresGuest && isLoggedIn) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
