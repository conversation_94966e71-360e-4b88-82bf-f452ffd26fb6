// 用户数据模型
export class User {
  constructor(data = {}) {
    this.objectId = data.objectId || null
    this.username = data.username || ''
    this.email = data.email || ''
    this.password = data.password || ''
    this.phone = data.phone || ''
    this.avatar = data.avatar || ''
    this.role = data.role || 'user' // user, admin
    this.status = data.status || 'active' // active, inactive
    this.createdAt = data.createdAt || null
    this.updatedAt = data.updatedAt || null
  }

  // 验证用户数据
  validate(isLogin = false) {
    const errors = []
    
    if (!this.username || this.username.trim() === '') {
      errors.push('用户名不能为空')
    }
    
    if (!isLogin) {
      if (!this.email || this.email.trim() === '') {
        errors.push('邮箱不能为空')
      } else if (!this.isValidEmail(this.email)) {
        errors.push('邮箱格式不正确')
      }
    }
    
    if (!this.password || this.password.length < 6) {
      errors.push('密码长度至少6位')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // 验证邮箱格式
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 转换为API请求格式
  toApiFormat() {
    return {
      username: this.username,
      email: this.email,
      password: this.password,
      phone: this.phone,
      avatar: this.avatar,
      role: this.role,
      status: this.status
    }
  }

  // 转换为登录格式
  toLoginFormat() {
    return {
      username: this.username,
      password: this.password
    }
  }

  // 从API响应创建实例
  static fromApiResponse(data) {
    return new User(data)
  }
}

// 用户角色枚举
export const USER_ROLES = [
  { value: 'user', label: '普通用户' },
  { value: 'admin', label: '管理员' }
]

// 用户状态枚举
export const USER_STATUS = [
  { value: 'active', label: '激活' },
  { value: 'inactive', label: '禁用' }
]
