// 商品数据模型
export class Product {
  constructor(data = {}) {
    this.objectId = data.objectId || null
    this.name = data.name || ''
    this.description = data.description || ''
    this.price = data.price || 0
    this.category = data.category || ''
    this.image = data.image || ''
    this.stock = data.stock || 0
    this.status = data.status || 'active' // active, inactive
    this.createdAt = data.createdAt || null
    this.updatedAt = data.updatedAt || null
  }

  // 验证商品数据
  validate() {
    const errors = []
    
    if (!this.name || this.name.trim() === '') {
      errors.push('商品名称不能为空')
    }
    
    if (!this.description || this.description.trim() === '') {
      errors.push('商品描述不能为空')
    }
    
    if (!this.price || this.price <= 0) {
      errors.push('商品价格必须大于0')
    }
    
    if (!this.category || this.category.trim() === '') {
      errors.push('商品分类不能为空')
    }
    
    if (this.stock < 0) {
      errors.push('商品库存不能为负数')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // 转换为API请求格式
  toApiFormat() {
    return {
      name: this.name,
      description: this.description,
      price: this.price,
      category: this.category,
      image: this.image,
      stock: this.stock,
      status: this.status
    }
  }

  // 从API响应创建实例
  static fromApiResponse(data) {
    return new Product(data)
  }
}

// 商品分类枚举
export const PRODUCT_CATEGORIES = [
  { value: 'electronics', label: '电子产品' },
  { value: 'clothing', label: '服装' },
  { value: 'books', label: '图书' },
  { value: 'home', label: '家居用品' },
  { value: 'sports', label: '运动用品' },
  { value: 'food', label: '食品' },
  { value: 'beauty', label: '美妆' },
  { value: 'toys', label: '玩具' }
]

// 商品状态枚举
export const PRODUCT_STATUS = [
  { value: 'active', label: '上架' },
  { value: 'inactive', label: '下架' }
]
