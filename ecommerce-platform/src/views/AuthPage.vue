<template>
  <div class="auth-page">
    <transition name="slide-fade" mode="out-in">
      <LoginForm
        v-if="currentView === 'login'"
        @login-success="handleLoginSuccess"
        @switch-to-register="currentView = 'register'"
      />
      <RegisterForm
        v-else
        @register-success="handleRegisterSuccess"
        @switch-to-login="currentView = 'login'"
      />
    </transition>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import LoginForm from '@/components/LoginForm.vue'
import RegisterForm from '@/components/RegisterForm.vue'

const router = useRouter()
const currentView = ref('login')

const handleLoginSuccess = (user) => {
  console.log('登录成功:', user)
  router.push('/dashboard')
}

const handleRegisterSuccess = (user) => {
  console.log('注册成功:', user)
  router.push('/dashboard')
}
</script>

<style scoped>
.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 过渡动画 */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
