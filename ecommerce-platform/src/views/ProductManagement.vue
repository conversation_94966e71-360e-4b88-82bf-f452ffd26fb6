<template>
  <div class="product-management">
    <div class="page-header">
      <h2>商品管理</h2>
      <el-button type="primary" @click="handleAddProduct">
        <el-icon><Plus /></el-icon>
        添加商品
      </el-button>
    </div>

    <ProductList
      ref="productListRef"
      @edit="handleEditProduct"
      @delete="handleDeleteProduct"
    />

    <ProductForm
      v-model="showForm"
      :product="currentProduct"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import ProductList from '@/components/ProductList.vue'
import ProductForm from '@/components/ProductForm.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ProductApi } from '@/api/productApi.js'

// 响应式数据
const showForm = ref(false)
const currentProduct = ref(null)
const productListRef = ref()

// 方法
const handleAddProduct = () => {
  currentProduct.value = null
  showForm.value = true
}

const handleEditProduct = (product) => {
  currentProduct.value = product
  showForm.value = true
}

const handleDeleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品 "${product.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await ProductApi.deleteProduct(product.objectId)
    ElMessage.success('商品删除成功')
    productListRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除商品失败')
      console.error(error)
    }
  }
}

const handleFormSuccess = () => {
  productListRef.value?.refresh()
}
</script>

<style scoped>
.product-management {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-weight: 500;
}
</style>
