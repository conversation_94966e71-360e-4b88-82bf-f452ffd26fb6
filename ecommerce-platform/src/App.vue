<script setup>
import { ref } from 'vue'
import ProductList from './components/ProductList.vue'
import ProductForm from './components/ProductForm.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ProductApi } from './api/productApi.js'

// 响应式数据
const showForm = ref(false)
const currentProduct = ref(null)
const productListRef = ref()

// 方法
const handleAddProduct = () => {
  currentProduct.value = null
  showForm.value = true
}

const handleEditProduct = (product) => {
  currentProduct.value = product
  showForm.value = true
}

const handleDeleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品 "${product.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await ProductApi.deleteProduct(product.objectId)
    ElMessage.success('商品删除成功')
    productListRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除商品失败')
      console.error(error)
    }
  }
}

const handleFormSuccess = () => {
  productListRef.value?.refresh()
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>简易电商平台管理系统</h1>
    </header>

    <main class="app-main">
      <div class="toolbar">
        <el-button type="primary" @click="handleAddProduct">
          添加商品
        </el-button>
      </div>

      <ProductList
        ref="productListRef"
        @edit="handleEditProduct"
        @delete="handleDeleteProduct"
      />

      <ProductForm
        v-model="showForm"
        :product="currentProduct"
        @success="handleFormSuccess"
      />
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 40px 20px;
  margin-bottom: 20px;
}

.app-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
  font-weight: 300;
}

.app-header p {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.app-main {
  width: 100%;
  padding: 0 20px;
}

.toolbar {
  margin-bottom: 20px;
  text-align: right;
}
</style>
