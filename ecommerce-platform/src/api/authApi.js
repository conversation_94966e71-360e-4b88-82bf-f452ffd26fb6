import request from '@/utils/request.js'
import { API_ENDPOINTS, DEV_CONFIG } from '@/config/api.js'
import { User } from '@/models/User.js'

// 模拟用户数据
const mockUsers = [
  {
    objectId: '1',
    username: 'admin',
    email: '<EMAIL>',
    password: '123456',
    phone: '13800138000',
    avatar: '',
    role: 'admin',
    status: 'active',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    objectId: '2',
    username: 'user1',
    email: '<EMAIL>',
    password: '123456',
    phone: '13800138001',
    avatar: '',
    role: 'user',
    status: 'active',
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z'
  }
]

// 模拟当前登录用户
let currentUser = null

// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 用户认证API服务类
export class AuthApi {
  
  // 用户注册
  static async register(userData) {
    if (DEV_CONFIG.USE_MOCK_API) {
      await delay(1000)
      
      const user = new User(userData)
      const validation = user.validate(false)
      
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      // 检查用户名是否已存在
      const existingUser = mockUsers.find(u => u.username === user.username)
      if (existingUser) {
        throw new Error('用户名已存在')
      }

      // 检查邮箱是否已存在
      const existingEmail = mockUsers.find(u => u.email === user.email)
      if (existingEmail) {
        throw new Error('邮箱已被注册')
      }

      const newUser = {
        ...user.toApiFormat(),
        objectId: String(mockUsers.length + 1),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      mockUsers.push(newUser)
      
      // 注册成功后自动登录
      currentUser = { ...newUser }
      delete currentUser.password // 不返回密码
      
      return User.fromApiResponse(currentUser)
    }

    try {
      const user = new User(userData)
      const validation = user.validate(false)
      
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }
      
      const response = await request.post('/users', user.toApiFormat())
      return User.fromApiResponse(response)
    } catch (error) {
      console.error('用户注册失败:', error)
      throw error
    }
  }

  // 用户登录
  static async login(credentials) {
    if (DEV_CONFIG.USE_MOCK_API) {
      await delay(800)
      
      const { username, password } = credentials
      
      if (!username || !password) {
        throw new Error('用户名和密码不能为空')
      }

      const user = mockUsers.find(u => 
        u.username === username && u.password === password
      )

      if (!user) {
        throw new Error('用户名或密码错误')
      }

      if (user.status !== 'active') {
        throw new Error('账户已被禁用')
      }

      currentUser = { ...user }
      delete currentUser.password // 不返回密码
      
      // 保存到localStorage
      localStorage.setItem('currentUser', JSON.stringify(currentUser))
      localStorage.setItem('isLoggedIn', 'true')
      
      return User.fromApiResponse(currentUser)
    }

    try {
      const user = new User(credentials)
      const validation = user.validate(true)
      
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }
      
      const response = await request.post('/login', user.toLoginFormat())
      
      // 保存登录状态
      localStorage.setItem('currentUser', JSON.stringify(response))
      localStorage.setItem('isLoggedIn', 'true')
      
      return User.fromApiResponse(response)
    } catch (error) {
      console.error('用户登录失败:', error)
      throw error
    }
  }

  // 用户登出
  static async logout() {
    if (DEV_CONFIG.USE_MOCK_API) {
      await delay(300)
      currentUser = null
      localStorage.removeItem('currentUser')
      localStorage.removeItem('isLoggedIn')
      return true
    }

    try {
      await request.post('/logout')
      localStorage.removeItem('currentUser')
      localStorage.removeItem('isLoggedIn')
      return true
    } catch (error) {
      console.error('用户登出失败:', error)
      // 即使请求失败也清除本地状态
      localStorage.removeItem('currentUser')
      localStorage.removeItem('isLoggedIn')
      return true
    }
  }

  // 获取当前用户信息
  static getCurrentUser() {
    if (DEV_CONFIG.USE_MOCK_API) {
      const savedUser = localStorage.getItem('currentUser')
      if (savedUser) {
        return User.fromApiResponse(JSON.parse(savedUser))
      }
      return null
    }

    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      return User.fromApiResponse(JSON.parse(savedUser))
    }
    return null
  }

  // 检查是否已登录
  static isLoggedIn() {
    return localStorage.getItem('isLoggedIn') === 'true'
  }

  // 检查是否是管理员
  static isAdmin() {
    const user = this.getCurrentUser()
    return user && user.role === 'admin'
  }

  // 更新用户信息
  static async updateProfile(userData) {
    if (DEV_CONFIG.USE_MOCK_API) {
      await delay(800)
      
      if (!currentUser) {
        throw new Error('用户未登录')
      }

      const updatedUser = {
        ...currentUser,
        ...userData,
        updatedAt: new Date().toISOString()
      }

      // 更新mockUsers中的数据
      const index = mockUsers.findIndex(u => u.objectId === currentUser.objectId)
      if (index !== -1) {
        mockUsers[index] = { ...updatedUser, password: mockUsers[index].password }
      }

      currentUser = updatedUser
      localStorage.setItem('currentUser', JSON.stringify(currentUser))
      
      return User.fromApiResponse(currentUser)
    }

    try {
      const user = this.getCurrentUser()
      if (!user) {
        throw new Error('用户未登录')
      }

      const response = await request.put(`/users/${user.objectId}`, userData)
      
      // 更新本地存储
      const updatedUser = { ...user, ...response }
      localStorage.setItem('currentUser', JSON.stringify(updatedUser))
      
      return User.fromApiResponse(updatedUser)
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  // 修改密码
  static async changePassword(oldPassword, newPassword) {
    if (DEV_CONFIG.USE_MOCK_API) {
      await delay(800)
      
      if (!currentUser) {
        throw new Error('用户未登录')
      }

      // 验证旧密码
      const user = mockUsers.find(u => u.objectId === currentUser.objectId)
      if (!user || user.password !== oldPassword) {
        throw new Error('原密码错误')
      }

      if (newPassword.length < 6) {
        throw new Error('新密码长度至少6位')
      }

      // 更新密码
      user.password = newPassword
      user.updatedAt = new Date().toISOString()
      
      return true
    }

    try {
      const response = await request.put('/updatePassword', {
        oldPassword,
        newPassword
      })
      return response
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }
}
