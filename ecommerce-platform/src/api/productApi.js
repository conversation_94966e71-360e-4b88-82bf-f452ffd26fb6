import request from '@/utils/request.js'
import { API_ENDPOINTS, DEV_CONFIG } from '@/config/api.js'
import { Product } from '@/models/Product.js'
import { MockProductApi } from './mockApi.js'

// 商品API服务类
export class ProductApi {

  // 获取商品列表
  static async getProducts(params = {}) {
    // 如果使用模拟API
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.getProducts(params)
    }

    try {
      const queryParams = new URLSearchParams()

      // 分页参数
      if (params.limit) queryParams.append('limit', params.limit)
      if (params.skip) queryParams.append('skip', params.skip)

      // 排序参数
      if (params.order) queryParams.append('order', params.order)

      // 搜索参数
      if (params.where) queryParams.append('where', JSON.stringify(params.where))

      const url = `${API_ENDPOINTS.PRODUCTS}?${queryParams.toString()}`
      const response = await request.get(url)

      return {
        results: response.results?.map(item => Product.fromApiResponse(item)) || [],
        count: response.count || 0
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      throw error
    }
  }

  // 根据ID获取商品详情
  static async getProductById(objectId) {
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.getProductById(objectId)
    }

    try {
      const response = await request.get(`${API_ENDPOINTS.PRODUCTS}/${objectId}`)
      return Product.fromApiResponse(response)
    } catch (error) {
      console.error('获取商品详情失败:', error)
      throw error
    }
  }

  // 创建商品
  static async createProduct(productData) {
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.createProduct(productData)
    }

    try {
      const product = new Product(productData)
      const validation = product.validate()

      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const response = await request.post(API_ENDPOINTS.PRODUCTS, product.toApiFormat())
      return Product.fromApiResponse({ ...product.toApiFormat(), ...response })
    } catch (error) {
      console.error('创建商品失败:', error)
      throw error
    }
  }

  // 更新商品
  static async updateProduct(objectId, productData) {
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.updateProduct(objectId, productData)
    }

    try {
      const product = new Product(productData)
      const validation = product.validate()

      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const response = await request.put(`${API_ENDPOINTS.PRODUCTS}/${objectId}`, product.toApiFormat())
      return Product.fromApiResponse({ ...product.toApiFormat(), objectId, ...response })
    } catch (error) {
      console.error('更新商品失败:', error)
      throw error
    }
  }

  // 删除商品
  static async deleteProduct(objectId) {
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.deleteProduct(objectId)
    }

    try {
      await request.delete(`${API_ENDPOINTS.PRODUCTS}/${objectId}`)
      return true
    } catch (error) {
      console.error('删除商品失败:', error)
      throw error
    }
  }

  // 搜索商品
  static async searchProducts(keyword, params = {}) {
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.searchProducts(keyword, params)
    }

    try {
      const where = {
        $or: [
          { name: { $regex: keyword, $options: 'i' } },
          { description: { $regex: keyword, $options: 'i' } }
        ]
      }

      return await this.getProducts({ ...params, where })
    } catch (error) {
      console.error('搜索商品失败:', error)
      throw error
    }
  }

  // 按分类获取商品
  static async getProductsByCategory(category, params = {}) {
    if (DEV_CONFIG.USE_MOCK_API) {
      return await MockProductApi.getProductsByCategory(category, params)
    }

    try {
      const where = { category }
      return await this.getProducts({ ...params, where })
    } catch (error) {
      console.error('按分类获取商品失败:', error)
      throw error
    }
  }
}
