// 模拟API - 用于演示和测试
import { Product } from '@/models/Product.js'

// 模拟数据
const mockProducts = [
  {
    objectId: '1',
    name: 'iPhone 15 Pro',
    description: '苹果最新款智能手机，配备A17 Pro芯片，拍照效果出色，性能强劲。',
    price: 7999,
    category: 'electronics',
    stock: 50,
    status: 'active',
    image: '/placeholder.svg',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    objectId: '2',
    name: 'Nike Air Max 270',
    description: '舒适透气的运动鞋，采用Air Max气垫技术，适合日常运动和休闲穿着。',
    price: 899,
    category: 'sports',
    stock: 100,
    status: 'active',
    image: '/placeholder.svg',
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z'
  },
  {
    objectId: '3',
    name: '《Vue.js设计与实现》',
    description: '深入理解Vue.js框架的设计原理，适合前端开发者学习进阶知识。',
    price: 89,
    category: 'books',
    stock: 200,
    status: 'active',
    image: '/placeholder.svg',
    createdAt: '2024-01-03T00:00:00.000Z',
    updatedAt: '2024-01-03T00:00:00.000Z'
  },
  {
    objectId: '4',
    name: '无线蓝牙耳机',
    description: '高品质音效，降噪功能，长续航，适合音乐爱好者和商务人士。',
    price: 299,
    category: 'electronics',
    stock: 75,
    status: 'active',
    image: '/placeholder.svg',
    createdAt: '2024-01-04T00:00:00.000Z',
    updatedAt: '2024-01-04T00:00:00.000Z'
  },
  {
    objectId: '5',
    name: '咖啡豆 - 蓝山风味',
    description: '精选优质咖啡豆，蓝山风味，口感醇厚，香气浓郁。',
    price: 128,
    category: 'food',
    stock: 30,
    status: 'active',
    image: '/placeholder.svg',
    createdAt: '2024-01-05T00:00:00.000Z',
    updatedAt: '2024-01-05T00:00:00.000Z'
  },
  {
    objectId: '6',
    name: '智能手表',
    description: '多功能智能手表，健康监测，运动追踪，消息提醒。',
    price: 1299,
    category: 'electronics',
    stock: 0,
    status: 'inactive',
    image: '/placeholder.svg',
    createdAt: '2024-01-06T00:00:00.000Z',
    updatedAt: '2024-01-06T00:00:00.000Z'
  }
]

// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟API类
export class MockProductApi {
  static products = [...mockProducts]
  static nextId = 7

  // 获取商品列表
  static async getProducts(params = {}) {
    await delay(500) // 模拟网络延迟

    let results = [...this.products]
    
    // 搜索过滤
    if (params.where) {
      const where = params.where
      if (where.category) {
        results = results.filter(p => p.category === where.category)
      }
      if (where.status) {
        results = results.filter(p => p.status === where.status)
      }
      if (where.$or) {
        // 搜索功能
        const searchTerms = where.$or
        results = results.filter(product => {
          return searchTerms.some(term => {
            if (term.name && term.name.$regex) {
              return product.name.toLowerCase().includes(term.name.$regex.toLowerCase())
            }
            if (term.description && term.description.$regex) {
              return product.description.toLowerCase().includes(term.description.$regex.toLowerCase())
            }
            return false
          })
        })
      }
    }

    // 排序
    if (params.order) {
      const [field, direction] = params.order.startsWith('-') 
        ? [params.order.slice(1), 'desc'] 
        : [params.order, 'asc']
      
      results.sort((a, b) => {
        if (direction === 'desc') {
          return b[field] > a[field] ? 1 : -1
        }
        return a[field] > b[field] ? 1 : -1
      })
    }

    // 分页
    const skip = params.skip || 0
    const limit = params.limit || 20
    const paginatedResults = results.slice(skip, skip + limit)

    return {
      results: paginatedResults.map(item => Product.fromApiResponse(item)),
      count: results.length
    }
  }

  // 根据ID获取商品详情
  static async getProductById(objectId) {
    await delay(300)
    
    const product = this.products.find(p => p.objectId === objectId)
    if (!product) {
      throw new Error('商品不存在')
    }
    
    return Product.fromApiResponse(product)
  }

  // 创建商品
  static async createProduct(productData) {
    await delay(500)
    
    const product = new Product(productData)
    const validation = product.validate()
    
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '))
    }

    const newProduct = {
      ...product.toApiFormat(),
      objectId: String(this.nextId++),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.products.unshift(newProduct)
    return Product.fromApiResponse(newProduct)
  }

  // 更新商品
  static async updateProduct(objectId, productData) {
    await delay(500)
    
    const index = this.products.findIndex(p => p.objectId === objectId)
    if (index === -1) {
      throw new Error('商品不存在')
    }

    const product = new Product(productData)
    const validation = product.validate()
    
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '))
    }

    const updatedProduct = {
      ...this.products[index],
      ...product.toApiFormat(),
      updatedAt: new Date().toISOString()
    }

    this.products[index] = updatedProduct
    return Product.fromApiResponse(updatedProduct)
  }

  // 删除商品
  static async deleteProduct(objectId) {
    await delay(300)
    
    const index = this.products.findIndex(p => p.objectId === objectId)
    if (index === -1) {
      throw new Error('商品不存在')
    }

    this.products.splice(index, 1)
    return true
  }

  // 搜索商品
  static async searchProducts(keyword, params = {}) {
    const where = {
      $or: [
        { name: { $regex: keyword, $options: 'i' } },
        { description: { $regex: keyword, $options: 'i' } }
      ]
    }
    
    return await this.getProducts({ ...params, where })
  }

  // 按分类获取商品
  static async getProductsByCategory(category, params = {}) {
    const where = { category }
    return await this.getProducts({ ...params, where })
  }
}
