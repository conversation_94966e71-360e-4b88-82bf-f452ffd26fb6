<template>
  <div class="login-container">
    <el-card class="login-card" shadow="always">
      <template #header>
        <div class="card-header">
          <h2>用户登录</h2>
          <p>欢迎回到电商平台管理系统</p>
        </div>
      </template>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        label-width="0"
        size="large"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            :prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="login-footer">
            <span>还没有账号？</span>
            <el-button type="text" @click="$emit('switch-to-register')">
              立即注册
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <div class="demo-accounts">
        <el-divider>演示账号</el-divider>
        <div class="demo-account-list">
          <el-tag
            class="demo-tag"
            @click="fillDemoAccount('admin')"
            style="cursor: pointer; margin-right: 10px;"
          >
            管理员: admin / 123456
          </el-tag>
          <el-tag
            class="demo-tag"
            type="success"
            @click="fillDemoAccount('user')"
            style="cursor: pointer;"
          >
            用户: user1 / 123456
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { User, Lock } from '@element-plus/icons-vue'
import { AuthApi } from '@/api/authApi.js'
import { ElMessage } from 'element-plus'

// 事件定义
const emit = defineEmits(['login-success', 'switch-to-register'])

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 方法
const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    loading.value = true

    const user = await AuthApi.login(loginForm)
    
    ElMessage.success(`欢迎回来，${user.username}！`)
    emit('login-success', user)
    
    // 重置表单
    loginForm.username = ''
    loginForm.password = ''
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('登录失败，请重试')
    }
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 填充演示账号
const fillDemoAccount = (type) => {
  if (type === 'admin') {
    loginForm.username = 'admin'
    loginForm.password = '123456'
  } else if (type === 'user') {
    loginForm.username = 'user1'
    loginForm.password = '123456'
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
}

.card-header {
  text-align: center;
  margin-bottom: 20px;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 500;
}

.card-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.demo-accounts {
  margin-top: 20px;
}

.demo-account-list {
  text-align: center;
  margin-top: 10px;
}

.demo-tag {
  margin-bottom: 8px;
  transition: all 0.3s;
}

.demo-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-divider__text) {
  font-size: 12px;
  color: #999;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input__inner) {
  height: 45px;
}

:deep(.el-button--large) {
  height: 45px;
  font-size: 16px;
}
</style>
