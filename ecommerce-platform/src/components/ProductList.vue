<template>
  <div class="product-list">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索商品名称或描述"
            @keyup.enter="handleSearch"
            clearable
          >
            <template #append>
              <el-button @click="handleSearch" :icon="Search">搜索</el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            @change="handleCategoryChange"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedStatus"
            placeholder="选择状态"
            @change="handleStatusChange"
            clearable
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters" :icon="Refresh">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 商品列表 -->
    <div class="product-grid" v-loading="loading">
      <div v-if="products.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无商品数据" />
      </div>
      <div v-else class="product-cards">
        <el-card
          v-for="product in products"
          :key="product.objectId"
          class="product-card"
          shadow="hover"
        >
          <div class="product-image">
            <img
              :src="product.image || '/placeholder-image.jpg'"
              :alt="product.name"
              @error="handleImageError"
            />
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-description">{{ product.description }}</p>
            <div class="product-meta">
              <span class="product-price">¥{{ product.price }}</span>
              <span class="product-stock">库存: {{ product.stock }}</span>
            </div>
            <div class="product-category">
              <el-tag size="small">{{ getCategoryLabel(product.category) }}</el-tag>
              <el-tag
                :type="product.status === 'active' ? 'success' : 'danger'"
                size="small"
              >
                {{ getStatusLabel(product.status) }}
              </el-tag>
            </div>
          </div>
          <div class="product-actions">
            <el-button size="small" @click="$emit('edit', product)">编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="$emit('delete', product)"
            >
              删除
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ProductApi } from '@/api/productApi.js'
import { PRODUCT_CATEGORIES, PRODUCT_STATUS } from '@/models/Product.js'
import { ElMessage } from 'element-plus'

// 响应式数据
const products = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 选项数据
const categories = PRODUCT_CATEGORIES
const statusOptions = PRODUCT_STATUS

// 事件定义
const emit = defineEmits(['edit', 'delete'])

// 计算属性
const getCategoryLabel = (value) => {
  const category = categories.find(cat => cat.value === value)
  return category ? category.label : value
}

const getStatusLabel = (value) => {
  const status = statusOptions.find(stat => stat.value === value)
  return status ? status.label : value
}

// 方法
const loadProducts = async () => {
  loading.value = true
  try {
    const params = {
      limit: pageSize.value,
      skip: (currentPage.value - 1) * pageSize.value,
      order: '-createdAt'
    }

    // 构建查询条件
    const where = {}
    if (selectedCategory.value) {
      where.category = selectedCategory.value
    }
    if (selectedStatus.value) {
      where.status = selectedStatus.value
    }
    if (where.category || where.status) {
      params.where = where
    }

    let result
    if (searchKeyword.value) {
      result = await ProductApi.searchProducts(searchKeyword.value, params)
    } else {
      result = await ProductApi.getProducts(params)
    }

    products.value = result.results
    total.value = result.count
  } catch (error) {
    ElMessage.error('加载商品列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadProducts()
}

const handleCategoryChange = () => {
  currentPage.value = 1
  loadProducts()
}

const handleStatusChange = () => {
  currentPage.value = 1
  loadProducts()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedCategory.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
  loadProducts()
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadProducts()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadProducts()
}

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg'
}

// 生命周期
onMounted(() => {
  loadProducts()
})

// 暴露方法给父组件
defineExpose({
  refresh: loadProducts
})
</script>

<style scoped>
.product-list {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.product-grid {
  min-height: 400px;
}

.product-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.product-card {
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image {
  height: 200px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 10px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  margin-bottom: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #333;
}

.product-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.product-price {
  font-size: 18px;
  font-weight: bold;
  color: #e74c3c;
}

.product-stock {
  font-size: 12px;
  color: #666;
}

.product-category {
  display: flex;
  gap: 8px;
}

.product-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
