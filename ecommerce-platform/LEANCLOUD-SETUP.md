# LeanCloud 数据表设置指南

## 你的应用信息
- **应用ID**: uAXscy7KXjSWw6Loswnc5Q4x-gzGzoHsz
- **控制台链接**: https://console.leancloud.cn/apps/uAXscy7KXjSWw6Loswnc5Q4x-gzGzoHsz/storage/data

## 步骤1: 获取App Key

1. 访问你的LeanCloud控制台: https://console.leancloud.cn/apps/uAXscy7KXjSWw6Loswnc5Q4x-gzGzoHsz
2. 点击左侧菜单 "设置" -> "应用凭证"
3. 复制 "App Key" 的值
4. 更新 `src/config/api.js` 文件中的 `APP_KEY` 和 `HEADERS['X-LC-Key']`

## 步骤2: 创建Product数据表

1. 在LeanCloud控制台，点击左侧菜单 "数据存储" -> "结构化数据"
2. 点击 "创建Class" 按钮
3. 输入Class名称: `Product`
4. 选择权限设置（建议选择"限制写入"）
5. 点击"创建"

## 步骤3: 添加数据表字段

创建Product表后，需要添加以下字段。点击表名进入表详情，然后点击"添加列"：

### 必需字段：
1. **name** (String)
   - 类型: String
   - 描述: 商品名称
   - 是否必需: 是

2. **description** (String)
   - 类型: String
   - 描述: 商品描述
   - 是否必需: 是

3. **price** (Number)
   - 类型: Number
   - 描述: 商品价格
   - 是否必需: 是

4. **category** (String)
   - 类型: String
   - 描述: 商品分类
   - 是否必需: 是
   - 可选值: electronics, clothing, books, home, sports, food, beauty, toys

5. **stock** (Number)
   - 类型: Number
   - 描述: 库存数量
   - 是否必需: 是
   - 默认值: 0

6. **status** (String)
   - 类型: String
   - 描述: 商品状态
   - 是否必需: 是
   - 可选值: active, inactive
   - 默认值: active

7. **image** (String)
   - 类型: String
   - 描述: 商品图片URL
   - 是否必需: 否

## 步骤4: 设置表权限

1. 在Product表页面，点击"其他" -> "权限设置"
2. 建议设置：
   - **find**: 所有用户可读
   - **get**: 所有用户可读
   - **create**: 所有用户可写（或根据需要限制）
   - **update**: 所有用户可写（或根据需要限制）
   - **delete**: 所有用户可写（或根据需要限制）

## 步骤5: 添加测试数据

可以手动添加一些测试数据：

```json
{
  "name": "iPhone 15 Pro",
  "description": "苹果最新款智能手机，配备A17 Pro芯片",
  "price": 7999,
  "category": "electronics",
  "stock": 50,
  "status": "active",
  "image": "https://via.placeholder.com/300x200/007bff/ffffff?text=iPhone+15+Pro"
}
```

```json
{
  "name": "Nike运动鞋",
  "description": "舒适透气的运动鞋，适合日常运动",
  "price": 599,
  "category": "sports",
  "stock": 100,
  "status": "active",
  "image": "https://via.placeholder.com/300x200/28a745/ffffff?text=Nike+Shoes"
}
```

```json
{
  "name": "《Vue.js设计与实现》",
  "description": "深入理解Vue.js框架的设计原理",
  "price": 89,
  "category": "books",
  "stock": 200,
  "status": "active",
  "image": "https://via.placeholder.com/300x200/ffc107/000000?text=Vue.js+Book"
}
```

## 步骤6: 测试API连接

1. 确保已更新 `src/config/api.js` 中的 `APP_KEY`
2. 重启开发服务器: `npm run dev`
3. 打开浏览器访问 http://localhost:5173
4. 查看是否能正常加载商品列表

## 故障排除

### 如果遇到跨域错误：
1. 检查Vite代理配置是否正确
2. 确保LeanCloud应用域名设置正确

### 如果遇到认证错误：
1. 检查APP_ID和APP_KEY是否正确
2. 确保请求头中的X-LC-Id和X-LC-Key设置正确

### 如果数据不显示：
1. 检查Product表是否创建成功
2. 检查表权限设置
3. 查看浏览器控制台的网络请求和错误信息

## 下一步

配置完成后，你就可以：
- 查看商品列表
- 添加新商品
- 编辑商品信息
- 删除商品
- 搜索和筛选商品

所有数据都会自动保存到LeanCloud云数据库中！
