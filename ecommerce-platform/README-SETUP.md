# 简易电商平台 - 配置说明

## 项目概述
这是一个基于Vue3 + Vite + Element Plus + LeanCloud的简易电商平台管理系统，实现了商品的增删改查功能。

## 技术栈
- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **HTTP客户端**: Axios
- **后端服务**: LeanCloud (Serverless)

## 配置LeanCloud

### 1.访问 https://www.leancloud.cn/ 注册账号 注册LeanCloud账号


### 2. 创建应用
1. 登录后点击"创建应用"
2. 输入应用名称，选择"开发版"
3. 创建成功后进入应用管理界面

### 3. 创建数据表
1. 进入"数据存储" -> "结构化数据"
2. 点击"创建Class"，创建名为"Product"的数据表
3. 添加以下字段：
   - name (String) - 商品名称
   - description (String) - 商品描述
   - price (Number) - 商品价格
   - category (String) - 商品分类
   - image (String) - 商品图片URL
   - stock (Number) - 库存数量
   - status (String) - 商品状态

### 4. 获取应用凭证
1. 进入"设置" -> "应用凭证"
2. 复制 App ID 和 App Key
3. 复制 REST API 服务器地址

### 5. 配置项目
编辑 `src/config/api.js` 文件，替换以下配置：

```javascript
export const API_CONFIG = {
  APP_ID: '你的App ID',
  APP_KEY: '你的App Key',
  SERVER_URL: '你的REST API服务器地址',
  // ...
}
```

同时更新 `vite.config.js` 中的代理配置：

```javascript
server: {
  proxy: {
    '/api': {
      target: '你的REST API服务器地址',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

## 功能特性

### 商品管理
- ✅ 商品列表展示（分页、搜索、筛选）
- ✅ 添加新商品
- ✅ 编辑商品信息
- ✅ 删除商品
- ✅ 商品分类管理
- ✅ 商品状态管理

### 用户体验
- ✅ 响应式设计
- ✅ 加载状态提示
- ✅ 错误处理
- ✅ 成功提示
- ✅ 表单验证
- ✅ 图片预览

## 运行项目

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 项目结构
```
src/
├── api/              # API服务层
│   └── productApi.js # 商品API
├── components/       # 组件
│   ├── ProductList.vue  # 商品列表
│   └── ProductForm.vue  # 商品表单
├── config/          # 配置文件
│   └── api.js       # API配置
├── models/          # 数据模型
│   └── Product.js   # 商品模型
├── utils/           # 工具函数
│   └── request.js   # HTTP请求封装
├── App.vue          # 根组件
└── main.js          # 入口文件
```

## 注意事项

1. **LeanCloud配置**: 确保正确配置LeanCloud的应用凭证
2. **跨域处理**: 开发环境通过Vite代理处理跨域，生产环境需要后端配置CORS
3. **图片上传**: 当前版本使用URL输入，可扩展为文件上传功能
4. **数据验证**: 前端和后端都应该进行数据验证
5. **错误处理**: 已实现基础错误处理，可根据需要扩展

## 扩展功能建议

- 用户认证和权限管理
- 订单管理系统
- 购物车功能
- 支付集成
- 商品评论和评分
- 库存预警
- 数据统计和报表
- 图片上传功能
- 批量操作
- 导入导出功能

## 故障排除

### 常见问题
1. **网络请求失败**: 检查LeanCloud配置和网络连接
2. **跨域错误**: 确保Vite代理配置正确
3. **数据不显示**: 检查API响应格式和数据结构
4. **样式问题**: 确保Element Plus正确引入

### 调试技巧
- 使用浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 使用Vue DevTools调试组件状态
- 查看LeanCloud控制台的API调用日志
